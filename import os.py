import os
import zipfile

# Define the project structure for the Flutter inventory app
project_name = "inventory_scanner"
base_dir = f"/mnt/data/{project_name}"

folders = [
    "lib",
    "lib/screens",
    "lib/models",
    "lib/services",
    "assets",
]

main_dart_content = '''import 'package:flutter/material.dart';

void main() {
  runApp(const InventoryApp());
}

class InventoryApp extends StatelessWidget {
  const InventoryApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Inventory Scanner',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const Scaffold(
        body: Center(
          child: Text('Inventory App Coming Soon...'),
        ),
      ),
    );
  }
}
'''

# Create the directory structure
for folder in folders:
    os.makedirs(os.path.join(base_dir, folder), exist_ok=True)

# Create a basic main.dart file
with open(os.path.join(base_dir, "lib", "main.dart"), "w") as f:
    f.write(main_dart_content)

# Zip the project folder
zip_path = f"/mnt/data/{project_name}.zip"
with zipfile.ZipFile(zip_path, 'w') as zipf:
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            file_path = os.path.join(root, file)
            zipf.write(file_path, arcname=os.path.relpath(file_path, base_dir))

zip_path
